<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{387d38d2-6ad1-4118-ab1d-9e817040136e}</ProjectGuid>
    <RootNamespace>DMALib</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir)imgui;$(ProjectDir)imgui\backends;$(ProjectDir)imgui\misc\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <AdditionalDependencies>d3d11.lib;d3dcompiler.lib;dxgi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(ProjectDir)imgui;$(ProjectDir)imgui\backends;$(ProjectDir)imgui\misc\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <AdditionalDependencies>d3d11.lib;d3dcompiler.lib;dxgi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="imgui\backends\imgui_impl_dx11.h" />
    <ClInclude Include="imgui\backends\imgui_impl_win32.h" />
    <ClInclude Include="imgui\imconfig.h" />
    <ClInclude Include="imgui\imgui.h" />
    <ClInclude Include="imgui\imgui_internal.h" />
    <ClInclude Include="imgui\imstb_rectpack.h" />
    <ClInclude Include="imgui\imstb_textedit.h" />
    <ClInclude Include="imgui\imstb_truetype.h" />
    <ClInclude Include="imgui\misc\cpp\imgui_stdlib.h" />
    <ClInclude Include="includes\DMAMemoryManagement\includes.h" />
    <ClInclude Include="includes\DMAMemoryManagement\MemoryObject\MemoryObject.h" />
    <ClInclude Include="includes\DMAMemoryManagement\MemoryObject\PointerChain.h" />
    <ClInclude Include="includes\DMAMemoryManagement\MemProcFS\leechcore.h" />
    <ClInclude Include="includes\DMAMemoryManagement\MemProcFS\vmmdll.h" />
    <ClInclude Include="includes\DMAMemoryManagement\StaticManager\StaticManager.h" />
    <ClInclude Include="includes\DMAMemoryManagement\Utils\Utils.h" />
    <ClInclude Include="includes\DMAMemoryManagement\VmmManager\VmmManager.h" />
    <ClInclude Include="includes\DMARender\IGameMap\IGameMap.h" />
    <ClInclude Include="includes\DMARender\IRadar\IRadar.h" />
    <ClInclude Include="includes\DMARender\RenderWindow\MapTransform.h" />
    <ClInclude Include="includes\DMARender\RenderWindow\RenderBridge\MapManager\MapManager.h" />
    <ClInclude Include="includes\DMARender\RenderWindow\RenderBridge\RenderBridge.h" />
    <ClInclude Include="includes\DMARender\RenderWindow\RenderWindow.h" />
    <ClInclude Include="includes\DMARender\ImageAllocator\ImageAllocator.h" />
    <ClInclude Include="includes\DMARender\ImageAllocator\stb_image.h" />
    <ClInclude Include="includes\DMARender\includes.h" />
    <ClInclude Include="includes\DMARender\IOverlay\IOverlay.h" />
    <ClInclude Include="includes\DMARender\Vectors\Vector2.h" />
    <ClInclude Include="includes\DMARender\Vectors\Vector3.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="DMALib.cpp" />
    <ClCompile Include="imgui\backends\imgui_impl_dx11.cpp" />
    <ClCompile Include="imgui\backends\imgui_impl_win32.cpp" />
    <ClCompile Include="imgui\imgui.cpp" />
    <ClCompile Include="imgui\imgui_demo.cpp" />
    <ClCompile Include="imgui\imgui_draw.cpp" />
    <ClCompile Include="imgui\imgui_tables.cpp" />
    <ClCompile Include="imgui\imgui_widgets.cpp" />
    <ClCompile Include="imgui\misc\cpp\imgui_stdlib.cpp" />
    <ClCompile Include="includes\DMAMemoryManagement\MemoryObject\MemoryObject.cpp" />
    <ClCompile Include="includes\DMAMemoryManagement\StaticManager\StaticManager.cpp" />
    <ClCompile Include="includes\DMAMemoryManagement\Utils\Utils.cpp" />
    <ClCompile Include="includes\DMAMemoryManagement\VmmManager\VmmManager.cpp" />
    <ClCompile Include="includes\DMARender\IGameMap\IGameMap.cpp" />
    <ClCompile Include="includes\DMARender\IRadar\IRadar.cpp" />
    <ClCompile Include="includes\DMARender\RenderWindow\RenderBridge\MapManager\MapManager.cpp" />
    <ClCompile Include="includes\DMARender\RenderWindow\RenderBridge\RenderBridge.cpp" />
    <ClCompile Include="includes\DMARender\RenderWindow\RenderWindow.cpp" />
    <ClCompile Include="includes\DMARender\ImageAllocator\ImageAllocator.cpp" />
    <ClCompile Include="includes\DMARender\IOverlay\IOverlay.cpp" />
    <ClCompile Include="includes\DMARender\Vectors\Vector3.cpp" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="libs\leechcore.lib" />
    <Library Include="libs\vmm.lib" />
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="libs\FTD3XX.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="libs\leechcore.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="libs\vmm.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="libs\dbghelp.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="libs\info.db">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="libs\symsrv.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="DMALib.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="..\dma-dayz-cpp\53ac2d01e41ff949c11c916309ae17b7.ico" />
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="libs\symsrv.yes">
      <FileType>Document</FileType>
    </CopyFileToFolders>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>